<?php
/**
 * Database Initialization Script
 * Run this script to set up the database with initial data
 */

require_once 'config.php';

echo "<h1>Database Initialization</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// Read and execute SQL file
$sql_file = 'database_setup.sql';
if (!file_exists($sql_file)) {
    echo "<p class='error'>Error: database_setup.sql file not found!</p>";
    exit;
}

$sql_content = file_get_contents($sql_file);
$sql_statements = explode(';', $sql_content);

echo "<h2>Executing Database Setup...</h2>";

$success_count = 0;
$error_count = 0;

foreach ($sql_statements as $statement) {
    $statement = trim($statement);
    if (empty($statement) || strpos($statement, '--') === 0 || strlen($statement) < 5) {
        continue; // Skip empty lines, comments, and very short statements
    }

    if (mysqli_query($link, $statement)) {
        $success_count++;
        echo "<p class='success'>✓ Executed: " . substr($statement, 0, 50) . "...</p>";
    } else {
        $error = mysqli_error($link);
        // Check if it's a "duplicate key" or "already exists" error - these can be ignored
        if (strpos($error, 'Duplicate key name') !== false ||
            strpos($error, 'already exists') !== false ||
            strpos($error, 'Duplicate entry') !== false) {
            echo "<p class='info'>⚠ Skipped (already exists): " . substr($statement, 0, 50) . "...</p>";
        } else {
            $error_count++;
            echo "<p class='error'>✗ Failed: " . substr($statement, 0, 50) . "... Error: " . $error . "</p>";
        }
    }
}

echo "<h2>Summary</h2>";
echo "<p class='info'>Successful statements: $success_count</p>";
echo "<p class='info'>Failed statements: $error_count</p>";

// Verify tables were created
echo "<h2>Verifying Tables...</h2>";
$tables = ['users', 'view_counter', 'orders'];
foreach ($tables as $table) {
    $result = mysqli_query($link, "SHOW TABLES LIKE '$table'");
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<p class='success'>✓ Table '$table' created successfully</p>";
        
        // Show table structure
        $structure = mysqli_query($link, "DESCRIBE $table");
        if ($structure) {
            echo "<details><summary>View $table structure</summary>";
            echo "<table border='1' style='border-collapse:collapse;margin:10px;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = mysqli_fetch_assoc($structure)) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table></details>";
        }
    } else {
        echo "<p class='error'>✗ Table '$table' was not created</p>";
    }
}

// Check if admin user exists
echo "<h2>Checking Admin User...</h2>";
$result = mysqli_query($link, "SELECT username FROM users WHERE username = 'admin'");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<p class='success'>✓ Admin user exists</p>";
} else {
    echo "<p class='info'>Creating admin user...</p>";
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (username, password, email, is_active) VALUES ('admin', '$admin_password', '<EMAIL>', 1)";
    if (mysqli_query($link, $sql)) {
        echo "<p class='success'>✓ Admin user created (username: admin, password: admin123)</p>";
    } else {
        echo "<p class='error'>✗ Failed to create admin user: " . mysqli_error($link) . "</p>";
    }
}

// Initialize view counter
echo "<h2>Initializing View Counter...</h2>";
$result = mysqli_query($link, "SELECT * FROM view_counter WHERE page_name = 'index'");
if ($result && mysqli_num_rows($result) > 0) {
    echo "<p class='success'>✓ View counter already initialized</p>";
} else {
    $sql = "INSERT INTO view_counter (page_name, view_count) VALUES ('index', 0)";
    if (mysqli_query($link, $sql)) {
        echo "<p class='success'>✓ View counter initialized</p>";
    } else {
        echo "<p class='error'>✗ Failed to initialize view counter: " . mysqli_error($link) . "</p>";
    }
}

// Create sample data (optional)
echo "<h2>Sample Data (Optional)</h2>";
echo "<p class='info'>You can create sample users and orders for testing:</p>";

// Sample user
$sample_username = 'testuser';
$result = mysqli_query($link, "SELECT username FROM users WHERE username = '$sample_username'");
if ($result && mysqli_num_rows($result) == 0) {
    $sample_password = password_hash('testpass', PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (username, password, email, is_active) VALUES ('$sample_username', '$sample_password', '<EMAIL>', 1)";
    if (mysqli_query($link, $sql)) {
        echo "<p class='success'>✓ Sample user created (username: testuser, password: testpass)</p>";
    } else {
        echo "<p class='error'>✗ Failed to create sample user</p>";
    }
} else {
    echo "<p class='info'>Sample user already exists</p>";
}

echo "<h2>Initialization Complete!</h2>";
echo "<p class='success'>Database setup is complete. You can now:</p>";
echo "<ul>";
echo "<li><a href='test_system.php'>Run system tests</a></li>";
echo "<li><a href='index.php'>Visit the main site</a></li>";
echo "<li>Login as admin (username: admin, password: admin123)</li>";
echo "<li>Login as test user (username: testuser, password: testpass)</li>";
echo "</ul>";

mysqli_close($link);
?>
