<?php
/**
 * Complete Database Rebuild Script
 * This script will recreate all tables and add default accounts
 */

require_once 'config.php';

echo "<h1>Database Rebuild Script</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} .info{color:blue;}</style>";

echo "<p class='warning'><strong>WARNING:</strong> This will completely rebuild the database!</p>";

// Step 1: Drop existing tables
echo "<h2>Step 1: Dropping Existing Tables</h2>";
$tables_to_drop = ['orders', 'view_counter', 'users'];

foreach ($tables_to_drop as $table) {
    $sql = "DROP TABLE IF EXISTS $table";
    if (mysqli_query($link, $sql)) {
        echo "<p class='success'>✓ Dropped table: $table</p>";
    } else {
        echo "<p class='error'>✗ Failed to drop table $table: " . mysqli_error($link) . "</p>";
    }
}

// Step 2: Create Users table with extended profile
echo "<h2>Step 2: Creating Users Table</h2>";
$users_sql = "
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    nickname VARCHAR(50),
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say') DEFAULT 'prefer_not_to_say',
    birthday DATE,
    language VARCHAR(10) DEFAULT 'en',
    email VARCHAR(100),
    phone_number VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_username (username),
    INDEX idx_email (email)
)";

if (mysqli_query($link, $users_sql)) {
    echo "<p class='success'>✓ Users table created successfully</p>";
} else {
    echo "<p class='error'>✗ Failed to create users table: " . mysqli_error($link) . "</p>";
}

// Step 3: Create View Counter table
echo "<h2>Step 3: Creating View Counter Table</h2>";
$view_counter_sql = "
CREATE TABLE view_counter (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) DEFAULT 'index',
    view_count INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_page (page_name)
)";

if (mysqli_query($link, $view_counter_sql)) {
    echo "<p class='success'>✓ View counter table created successfully</p>";
} else {
    echo "<p class='error'>✗ Failed to create view counter table: " . mysqli_error($link) . "</p>";
}

// Step 4: Create Orders table with pricing
echo "<h2>Step 4: Creating Orders Table</h2>";
$orders_sql = "
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    services JSON NOT NULL,
    notes TEXT,
    budget_range VARCHAR(50),
    estimated_price DECIMAL(10,2) DEFAULT NULL,
    final_price DECIMAL(10,2) DEFAULT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
)";

if (mysqli_query($link, $orders_sql)) {
    echo "<p class='success'>✓ Orders table created successfully</p>";
} else {
    echo "<p class='error'>✗ Failed to create orders table: " . mysqli_error($link) . "</p>";
}

// Step 5: Create additional indexes
echo "<h2>Step 5: Creating Additional Indexes</h2>";
$index_sql = "CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)";
if (mysqli_query($link, $index_sql)) {
    echo "<p class='success'>✓ Additional indexes created</p>";
} else {
    echo "<p class='error'>✗ Failed to create additional indexes: " . mysqli_error($link) . "</p>";
}

// Step 6: Initialize view counter
echo "<h2>Step 6: Initializing View Counter</h2>";
$init_counter_sql = "INSERT INTO view_counter (page_name, view_count) VALUES ('index', 0)";
if (mysqli_query($link, $init_counter_sql)) {
    echo "<p class='success'>✓ View counter initialized</p>";
} else {
    echo "<p class='error'>✗ Failed to initialize view counter: " . mysqli_error($link) . "</p>";
}

// Step 7: Create Admin Account
echo "<h2>Step 7: Creating Admin Account</h2>";
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);
$admin_sql = "INSERT INTO users (username, password, first_name, last_name, email, phone_number, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)";
$stmt = mysqli_prepare($link, $admin_sql);
if ($stmt) {
    mysqli_stmt_bind_param($stmt, "ssssssi", 
        $admin_username = 'admin',
        $admin_password,
        $admin_first = 'System',
        $admin_last = 'Administrator',
        $admin_email = '<EMAIL>',
        $admin_phone = '******-0001',
        $admin_active = 1
    );
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p class='success'>✓ Admin account created</p>";
        echo "<p class='info'>Username: admin | Password: admin123</p>";
    } else {
        echo "<p class='error'>✗ Failed to create admin account: " . mysqli_stmt_error($stmt) . "</p>";
    }
    mysqli_stmt_close($stmt);
} else {
    echo "<p class='error'>✗ Failed to prepare admin account statement: " . mysqli_error($link) . "</p>";
}

// Step 8: Create Test Member Account
echo "<h2>Step 8: Creating Test Member Account</h2>";
$test_password = password_hash('test123', PASSWORD_DEFAULT);
$test_sql = "INSERT INTO users (username, password, first_name, last_name, nickname, gender, birthday, language, email, phone_number, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
$stmt = mysqli_prepare($link, $test_sql);
if ($stmt) {
    mysqli_stmt_bind_param($stmt, "ssssssssssi",
        $test_username = 'testuser',
        $test_password,
        $test_first = 'John',
        $test_last = 'Doe',
        $test_nickname = 'Johnny',
        $test_gender = 'male',
        $test_birthday = '1990-01-15',
        $test_language = 'en',
        $test_email = '<EMAIL>',
        $test_phone = '******-0123',
        $test_active = 1
    );
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p class='success'>✓ Test member account created</p>";
        echo "<p class='info'>Username: testuser | Password: test123</p>";
    } else {
        echo "<p class='error'>✗ Failed to create test member account: " . mysqli_stmt_error($stmt) . "</p>";
    }
    mysqli_stmt_close($stmt);
} else {
    echo "<p class='error'>✗ Failed to prepare test member statement: " . mysqli_error($link) . "</p>";
}

// Step 9: Create Sample Order for Testing
echo "<h2>Step 9: Creating Sample Order</h2>";
$sample_services = json_encode(['VIP Custom PC', 'Optimize Photo & Video']);
$order_sql = "INSERT INTO orders (user_id, username, services, notes, budget_range, status) VALUES (?, ?, ?, ?, ?, ?)";
$stmt = mysqli_prepare($link, $order_sql);
if ($stmt) {
    mysqli_stmt_bind_param($stmt, "isssss",
        $order_user_id = 2, // testuser's ID
        $order_username = 'testuser',
        $sample_services,
        $order_notes = 'Sample order for testing purposes. Need a gaming PC with RGB lighting.',
        $order_budget = '2000_3000',
        $order_status = 'pending'
    );
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p class='success'>✓ Sample order created</p>";
    } else {
        echo "<p class='error'>✗ Failed to create sample order: " . mysqli_stmt_error($stmt) . "</p>";
    }
    mysqli_stmt_close($stmt);
} else {
    echo "<p class='error'>✗ Failed to prepare sample order statement: " . mysqli_error($link) . "</p>";
}

// Step 10: Verification
echo "<h2>Step 10: Database Verification</h2>";
$tables = ['users', 'view_counter', 'orders'];
foreach ($tables as $table) {
    $result = mysqli_query($link, "SELECT COUNT(*) as count FROM $table");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "<p class='success'>✓ Table '$table' has {$row['count']} records</p>";
    } else {
        echo "<p class='error'>✗ Failed to verify table '$table'</p>";
    }
}

echo "<h2>Database Rebuild Complete!</h2>";
echo "<div style='background-color:#e8f5e8;padding:20px;border-radius:10px;margin:20px 0;'>";
echo "<h3>🎉 Ready to Use!</h3>";
echo "<p><strong>Admin Account:</strong></p>";
echo "<ul><li>Username: <code>admin</code></li><li>Password: <code>admin123</code></li></ul>";
echo "<p><strong>Test Member Account:</strong></p>";
echo "<ul><li>Username: <code>testuser</code></li><li>Password: <code>test123</code></li></ul>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li><a href='index.php'>Visit Main Site</a></li>";
echo "<li><a href='test_system.php'>Run System Tests</a></li>";
echo "<li>Test registration with new extended form</li>";
echo "<li>Test VIP Custom PC budget selection</li>";
echo "<li>Test admin price management</li>";
echo "</ul>";
echo "</div>";

mysqli_close($link);
?>
