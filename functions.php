<?php
require_once 'config.php';

// Get and update view count using database
function get_and_update_view_count($page_name = 'index') {
    global $link;

    // First, try to get current count
    $sql = "SELECT view_count FROM view_counter WHERE page_name = ?";
    $stmt = execute_query($link, $sql, "s", [$page_name]);

    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);

        if ($row) {
            $current_count = $row['view_count'];
        } else {
            $current_count = 0;
        }
    } else {
        $current_count = 0;
    }

    // Increment the count
    $new_count = $current_count + 1;

    // Update or insert the count
    $sql = "INSERT INTO view_counter (page_name, view_count) VALUES (?, ?)
            ON DUPLICATE KEY UPDATE view_count = ?, last_updated = CURRENT_TIMESTAMP";
    $stmt = execute_query($link, $sql, "sii", [$page_name, $new_count, $new_count]);

    if ($stmt) {
        mysqli_stmt_close($stmt);
        return $new_count;
    } else {
        // Fallback to file-based counter if database fails
        return get_file_based_view_count();
    }
}

// Fallback function for file-based counter
function get_file_based_view_count() {
    $count_file = 'counter.txt';
    if (file_exists($count_file)) {
        $count = (int)file_get_contents($count_file);
    } else {
        $count = 0;
    }
    $count++;
    file_put_contents($count_file, $count);
    return $count;
}

// Get current view count without incrementing
function get_current_view_count($page_name = 'index') {
    global $link;

    $sql = "SELECT view_count FROM view_counter WHERE page_name = ?";
    $stmt = execute_query($link, $sql, "s", [$page_name]);

    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);

        if ($row) {
            return $row['view_count'];
        }
    }

    return 0;
}

// Function to validate user input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to validate username with enhanced rules
function validate_username($username) {
    // Length check: 6-20 characters
    if (strlen($username) < 6 || strlen($username) > 20) {
        return false;
    }

    // Must start with letter or number (not special character)
    if (!preg_match('/^[a-zA-Z0-9]/', $username)) {
        return false;
    }

    // Cannot be all numbers
    if (preg_match('/^[0-9]+$/', $username)) {
        return false;
    }

    // Only allow letters, numbers, and periods
    if (!preg_match('/^[a-zA-Z0-9.]+$/', $username)) {
        return false;
    }

    // Check for sensitive words (case insensitive)
    $sensitive_words = ['admin', 'kelvinkms', 'gm'];
    $username_lower = strtolower($username);
    foreach ($sensitive_words as $word) {
        if (strpos($username_lower, $word) !== false) {
            return false;
        }
    }

    return true;
}

// Function to validate password strength with enhanced rules
function validate_password($password) {
    // At least 10 characters
    if (strlen($password) < 10) {
        return false;
    }

    // Must contain uppercase letter
    if (!preg_match('/[A-Z]/', $password)) {
        return false;
    }

    // Must contain lowercase letter
    if (!preg_match('/[a-z]/', $password)) {
        return false;
    }

    // Must contain number
    if (!preg_match('/[0-9]/', $password)) {
        return false;
    }

    // Must contain special character
    if (!preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password)) {
        return false;
    }

    return true;
}

// Function to validate age (must be at least 13 years old)
function validate_age($birthday) {
    $birth_date = new DateTime($birthday);
    $today = new DateTime();
    $age = $today->diff($birth_date)->y;

    return $age >= 13;
}
?>