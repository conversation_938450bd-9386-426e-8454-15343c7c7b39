<!DOCTYPE html>
<html lang="en">
<head>
    <title>KelvinKMS.com</title>
    <meta charset="UTF-8" />
    <meta name="robots" content="noarchive" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="author" content="Kelvin KMS" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="icon" type="image/png" sizes="16x16" href="/Favicon/KMS_Logo_16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/Favicon/KMS_Logo_32.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/Favicon/KMS_Logo_96.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/Favicon/KMS_Logo_512.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/Favicon/KMS_Logo_152.png" />
    <link rel="mask-icon" href="/Favicon/KMS_Logo.svg" color="#5bbad5" />
    <link rel="manifest" href="/manifest.json" />
    <!-- CSS Files -->
    <link rel="stylesheet" href="CSS/main.css" />
    <link rel="stylesheet" href="CSS/style.css" />
    <link rel="stylesheet" href="CSS/custom-modal.css" />
</head>
<body>
    <!-- Site Info and Authentication -->
    <div class="site-info">
        <div class="view-counter">Page Views: <span id="counter">0</span></div>
        <div class="auth-buttons">
            <button id="loginBtn">Login</button>
            <button id="registerBtn">Register</button>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="modal">
        <div class="modal-content register-modal-content">
            <span class="close-btn" id="closeRegister">×</span>
            <h2>Create Account</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="regFirstName">First Name :</label>
                    <input type="text" id="regFirstName" placeholder="Enter your first name" required>
                </div>

                <div class="form-group">
                    <label for="regLastName">Last Name :</label>
                    <input type="text" id="regLastName" placeholder="Enter your last name" required>
                </div>

                <div class="form-group">
                    <label for="regNickname">Nickname :</label>
                    <input type="text" id="regNickname" placeholder="Enter your nickname (optional)">
                </div>

                <div class="form-group">
                    <label for="regUsername">Username :</label>
                    <input type="text" id="regUsername" placeholder="Enter your username" required>
                    <div class="username-hint">Use English letters, numbers, and periods. 6-20 characters.</div>
                    <div class="username-validation" id="usernameValidation"></div>
                </div>

                <div class="form-group">
                    <label>Gender :</label>
                    <div class="gender-buttons">
                        <button type="button" class="gender-btn" data-gender="male">
                            <span class="gender-icon">♂</span>
                            <span>Male</span>
                        </button>
                        <button type="button" class="gender-btn" data-gender="female">
                            <span class="gender-icon">♀</span>
                            <span>Female</span>
                        </button>
                    </div>
                    <input type="hidden" id="regGender" required>
                </div>

                <div class="form-group">
                    <label for="regBirthday">Birthday :</label>
                    <input type="date" id="regBirthday" required>
                    <div class="age-validation" id="ageValidation"></div>
                </div>

                <div class="form-group">
                    <label>Language :</label>
                    <div class="language-buttons">
                        <button type="button" class="lang-btn" data-lang="en">English</button>
                        <button type="button" class="lang-btn" data-lang="zh">中文</button>
                        <button type="button" class="lang-btn" data-lang="es">Español</button>
                        <button type="button" class="lang-btn" data-lang="ja">日本語</button>
                        <button type="button" class="lang-btn" data-lang="ko">한국어</button>
                        <button type="button" class="lang-btn" data-lang="other">Other</button>
                    </div>
                    <input type="hidden" id="regLanguage" required>
                </div>

                <div class="form-group">
                    <label for="regEmail">E-mail :</label>
                    <input type="email" id="regEmail" placeholder="Enter your email address" required>
                    <div class="email-validation" id="emailValidation"></div>
                </div>

                <div class="form-group">
                    <label for="regEmailConfirm">Confirm E-mail :</label>
                    <input type="email" id="regEmailConfirm" placeholder="Confirm your email address" required onpaste="return false">
                </div>

                <div class="form-group">
                    <label for="regPhone">Phone Number :</label>
                    <div class="phone-input-group">
                        <input type="tel" id="regPhone" placeholder="Enter your phone number" pattern="[0-9]*" inputmode="numeric" maxlength="15" required>
                        <button type="button" id="sendSmsBtn" class="sms-btn">Send Code</button>
                    </div>
                    <div class="sms-verification" id="smsVerification" style="display: none;">
                        <input type="text" id="smsCode" placeholder="Enter verification code" maxlength="6">
                        <button type="button" id="verifySmsBtn" class="verify-btn">Verify</button>
                    </div>
                    <div class="sms-status" id="smsStatus"></div>
                </div>

                <div class="form-group">
                    <label for="regPassword">Password :</label>
                    <input type="password" id="regPassword" placeholder="Enter your password" required>
                    <div class="password-requirements">
                        <div class="requirement-title">Password must contain:</div>
                        <div class="requirement" id="req-length">At least 10 characters</div>
                        <div class="requirement" id="req-uppercase">Uppercase letter (A-Z)</div>
                        <div class="requirement" id="req-lowercase">Lowercase letter (a-z)</div>
                        <div class="requirement" id="req-number">Number (0-9)</div>
                        <div class="requirement" id="req-special">Special character (!@#$%^&*...)</div>
                    </div>
                    <div class="password-progress-wrapper">
                        <div class="password-progress" id="passwordProgress"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="regPasswordConfirm">Confirm Password :</label>
                    <input type="password" id="regPasswordConfirm" placeholder="Confirm your password" required onpaste="return false">
                    <div class="password-match" id="passwordMatch"></div>
                </div>

                <button type="submit" class="register-submit-btn">Register</button>
            </form>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" id="closeLogin">×</span>
            <h2>Member Login</h2>
            <form id="loginForm">
                <input type="text" id="loginUsername" placeholder="Username" required>
                <input type="password" id="loginPassword" placeholder="Password" required>
                <button type="submit">Login</button>
            </form>
        </div>
    </div>

    <!-- Original page content -->
    <div class="container">
        <!-- ... your existing sections from #home to #copyright ... -->
        <div id="home" class="section">
            <h1>Welcome to<br />KelvinKMS.com</h1>
            <p>Hello! My name is Kelvin.</p>
            <p>I offer some services for you.</p>
            <p>1 : VIP Custom PC</p>
            <p>2 : Optimize Photo & Video</p>
            <p>3 : Print Service</p>
            <p>4 : All in 1 Website Service</p>
            <p>5 : Question Consult</p>
            <p>6 : Useful PC Softwares & iOS Apps</p>
            <p>7 : Best Music Player</p>
        </div>
        <div id="portfolio" class="section">
            <h2>VIP Custom PC</h2>
            <p>The most beautiful custom PC you can expect.</p>
            <p>I make all custom PCs one by one.</p>
            <p>Contact me for details.</p>
        </div>
        <div class="slideshow-container">
            <img class="slide" src="/KMS_VIP_PC/VIP_PC_1.jpg" alt="Slide 1">
            <img class="slide" src="/KMS_VIP_PC/VIP_PC_2.jpg" alt="Slide 2">
        </div>
        <div id="optimize" class="section">
            <h2>Optimize Photo & Video</h2>
            <p>I can optimize your low quality photo or video to the best quality.</p>
            <p>I can remove photo or video watermark.</p>
            <p>One photo optimize = $3</p>
            <p>One photo watermark remove = $3</p>
            <p>Over 10 photos = 10% discount</p>
            <p>One video 1 min 30FPS 1080P = $2</p>
            <p>Video Watermark Remove</p>
            <p>Easy mode = $10 each up to 30 mins</p>
            <p>Hard mode = $30 each up to 30 mins</p>
            <p><strong>Notice :</strong> Price can be changed if I need to adjust.</p>
        </div>
        <div id="print-service" class="section">
            <h2>Print Service</h2>
            <p>We offer print service</p>
            <p>You can print your documents or photos</p>
            <p>Very high quality</p>
            <p><strong>(Letter size) 32 lb Premium Paper</strong></p>
            <p>Black $0.5 , Color $1</p>
            <p><strong>(Letter size) 110 lb Ultra Premium Paper</strong></p>
            <p>Black $1 , Color $2</p>
            <p><strong>(Letter size) Premium 45 lbs Photo Paper (Matte)</strong></p>
            <p>Black $1.5 , Color $3</p>
            <p><strong>(Letter size) Five Stars Premium 70 lbs Photo Paper (Glossy)</strong></p>
            <p>Black $2.5 , Color $5</p>
            <p><strong>(Letter size) 5 mil Thermal Laminating Pouch</strong> $5</p>
            <p><strong>4"x 6" Five Stars Premium 70 lbs Photo Paper</strong></p>
            <p>Black & Color $1 each</p>
            <p><strong>4"x6" 5 mil Thermal Laminating Pouch</strong> $3</p>
            <p>Price includes service fee</p>
            <p>Shipping & handling fee = $3 ~ $10 based on volume and paper size</p>
            <p>10% discount applied when you print over 10 papers</p>
            <p><strong>Beautiful Photo Album for sale</strong></p>
            <p>Basic Photo Album 36 Photos = $5</p>
            <p>Premium Hard Cover Pink Flower 52 Photos = $15 each</p>
            <p>Premium Hard Cover Pink Flower 300 Photos = $35 each</p>
            <p>Premium Hard Cover Pink Flower 600 Photos = $55 each</p>
            <p><strong>Notice :</strong> Price can be changed if I need to adjust.</p>
            <p><strong>Privacy Policy</strong></p>
            <p>All your documents or photos we print will be deleted after the print job is completed.</p>
            <p>We do not keep backup.</p>
        </div>
        <div id="website-service" class="section">
            <h2>All in 1 Website Service</h2>
            <p>I can design a website that can be used as your business presentation.</p>
            <p>All you need to do is to provide your business info.</p>
            <p>I will design both desktop and mobile version websites.</p>
            <p>Choose your own URL name and host your website from our server.</p>
            <p>You just need to pay a monthly payment (price varies) to keep your website running.</p>
        </div>
        <div id="question-consult" class="section">
            <h2>Question Consult</h2>
            <p>You can ask questions for the answers. Price vary depend on complexity. contact me for details.</p>
        </div>
        <div id="useful-softwares" class="section">
            <h2>Useful PC Softwares & iOS Apps</h2>
            <p>Our service is to introduce the best PC softwares or iOS Apps for your works or entertainments.</p>
            <p><strong>PS:</strong> Some softwares against our services cannot be offered.</p>
            <p>Price vary depending on software value.</p>
            <p>Base price is $60~$200 introduction fee.</p>
            <p>Introduction fee includes basic tutorial if you need.</p>
            <p><strong>Optional:</strong></p>
            <p>In-home installation fee = $4 per mile + $30</p>
            <p>Software license fee = License + $30</p>
            <p>Or Monthly license = $5 per month</p>
        </div>
        <div id="best-music" class="section">
            <h2>Best Music Player</h2>
            <p>I can install the best music player for you.</p>
            <p><strong>Price</strong></p>
            <p>Installation fee = $100</p>
            <p>Month Premium Subscription fee = $18</p>
        </div>
        <div id="copyright" class="section">
            <h2>All rights strictly reserved</h2>
            <p>Copyright 2025</p>
            <p>KelvinKMS.com</p>
        </div>
    </div>
    <div class="nav">
        <button onclick="scrollToSection('home')">Home</button>
        <button onclick="scrollToSection('portfolio')">VIP Custom PC</button>
        <button onclick="scrollToSection('optimize')">Optimize Photo & Video</button>
        <button onclick="scrollToSection('print-service')">Print Service</button>
        <button onclick="scrollToSection('website-service')">All in 1 Website</button>
        <button onclick="scrollToSection('question-consult')">Question Consult</button>
        <button onclick="scrollToSection('useful-softwares')">Useful Softwares & Apps</button>
        <button onclick="scrollToSection('best-music')">Best Music Player</button>
    </div>
    <div class="mobile-nav">
        <button onclick="scrollToSection('home')">🏠</button>
    </div>

    <!-- JavaScript Files -->
    <script src="JS/custom-modal.js"></script>
    <script src="JS/main.js"></script>
    <script src="JS/forms.js"></script>
</body>
</html>