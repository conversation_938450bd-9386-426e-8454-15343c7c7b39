# KelvinKMS.com Installation Guide

## Database Setup

1. **Create MySQL Database**
   - Open phpMyAdmin or MySQL command line
   - Run the SQL script in `database_setup.sql`
   - This will create the `kelvinkms` database with required tables

2. **Database Configuration**
   - The database configuration is in `config.php`
   - Default settings:
     - Server: localhost
     - Username: root
     - Password: (empty)
     - Database: kelvinkms

## Features Implemented

### 1. English Interface
- All Chinese text has been converted to English
- Updated forms, buttons, and messages
- Improved user experience with clear English labels

### 2. MySQL Database Integration
- **Users Table**: Stores user accounts with secure password hashing
- **View Counter Table**: Tracks page views in database instead of files
- **Orders Table**: Stores service orders with status tracking

### 3. Authentication System
- Secure user registration with validation
- Password hashing using <PERSON><PERSON>'s password_hash()
- Session-based login system
- Admin account (username: admin, password: admin123)

### 4. Order Management
- Users can place service orders through member area
- <PERSON><PERSON> can view and manage all orders
- Order status tracking (pending, processing, completed, cancelled)

## File Structure

- `index.php` - Main homepage with registration/login modals
- `config.php` - Database configuration and connection functions
- `functions.php` - Utility functions for database operations
- `register.php` - User registration backend
- `login.php` - User authentication backend
- `member.php` - Member area for placing orders
- `admin.php` - Admin dashboard for order management
- `logout.php` - Session cleanup and logout
- `submit_order.php` - Order submission backend
- `update_order_status.php` - Admin order status updates
- `get_view_count.php` - View counter API
- `database_setup.sql` - Database schema and initial data

## Testing Instructions

### 1. Database Setup Test
- Import `database_setup.sql` into MySQL
- Verify tables are created: users, view_counter, orders

### 2. Registration Test
- Go to homepage (index.php)
- Click "Register" button
- Fill in username, password, and optional email
- Should show success message and switch to login modal

### 3. Login Test
- Use registered credentials to login
- Should redirect to member.php
- Admin login: username "admin", password "admin123" → redirects to admin.php

### 4. View Counter Test
- Refresh the homepage multiple times
- View counter should increment in database
- Check view_counter table in database

### 5. Order System Test
- Login as regular user
- Select services and add notes
- Submit order
- Login as admin to view and manage orders

## Security Features

- Password hashing with PHP's password_hash()
- SQL injection prevention with prepared statements
- Input sanitization and validation
- Session-based authentication
- Admin privilege checking

## Troubleshooting

1. **Database Connection Issues**
   - Check MySQL service is running
   - Verify database credentials in config.php
   - Ensure kelvinkms database exists

2. **Registration/Login Not Working**
   - Check browser console for JavaScript errors
   - Verify PHP error logs
   - Ensure all PHP files have proper permissions

3. **View Counter Not Updating**
   - Check if view_counter table exists
   - Verify get_view_count.php is accessible
   - Check browser network tab for API calls

## Admin Access

- Username: admin
- Password: admin123
- Access: admin.php (order management)

## Default User for Testing

After running database_setup.sql, you can create test users through the registration form or use the admin account for immediate access.
