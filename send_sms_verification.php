<?php
session_start();
header('Content-Type: application/json');

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $phone_number = trim($_POST['phone_number']);
    
    if (empty($phone_number)) {
        echo json_encode([
            'success' => false,
            'message' => 'Phone number is required.'
        ]);
        exit;
    }
    
    // Generate a 6-digit verification code
    $verification_code = sprintf("%06d", mt_rand(1, 999999));
    
    // Store the verification code in session (in real implementation, you would send SMS)
    $_SESSION['sms_verification_code'] = $verification_code;
    $_SESSION['sms_phone_number'] = $phone_number;
    $_SESSION['sms_code_time'] = time();
    
    // In a real implementation, you would send SMS here
    // For demo purposes, we'll just return success
    echo json_encode([
        'success' => true,
        'message' => 'Verification code sent successfully.',
        'demo_code' => $verification_code // Remove this in production
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
