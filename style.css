body {
    font-family: sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 800px;
    margin: auto;
    background: #fff;
    padding: 40px 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    text-align: center;
}

.view-counter {
    background: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    display: inline-block;
    margin-bottom: 10px;
}

.top-left-auth {
    margin-bottom: 20px;
}

.top-left-auth button {
    padding: 10px 15px;
    border: none;
    background-color: #333;
    color: white;
    cursor: pointer;
    border-radius: 5px;
    margin-right: 10px;
}

.top-left-auth button:hover {
    background-color: #555;
}

/* Modal (background) */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: hidden;
    background-color: rgb(24,101,134);
    padding-top: 60px;
}

/* Modal Content/Box */
.modal-content {
    background-color: #0cbda0;
    margin: 5% auto;
    padding: 16px;
    border: 1px solid #00ffcb;
    width: 500px;
    max-width: 500px;
    border-radius: 20px;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
    animation-name: animatetop;
    animation-duration: 0.4s
}

/* Enhanced Register Modal with Glassmorphism */
.register-modal-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 20px !important;
    background: rgba(255, 255, 255, 0.15) !important;
    box-shadow:
        0 8px 32px 0 rgba(31, 38, 135, 0.37),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(255, 255, 255, 0.1) !important;
    -webkit-backdrop-filter: blur(20px) saturate(180%) !important;
    backdrop-filter: blur(20px) saturate(180%) !important;
    width: 70% !important;
    max-width: 1000px !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    padding: 30px !important;
    font-size: 20px !important;
    position: relative !important;
}

.register-modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    border-radius: 20px;
    pointer-events: none;
    z-index: -1;
}

/* Mobile responsive for register modal */
@media (max-width: 768px) {
    .register-modal-content {
        width: 100%;
        height: 100vh;
        max-height: 100vh;
        margin: 0;
        border-radius: 0;
        padding: 16px;
    }
}

/* Add Animation */
@keyframes animatetop {
    from {top: -300px; opacity: 0}
    to {top: 0; opacity: 1}
}

/* The Close Button */
.close {
    color: #75ffec;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: rgb(255, 191, 0);
    text-decoration: none;
    cursor: pointer;
}

.modal h2 {
    text-align: center;
}

.modal form {
    display: flex;
    flex-direction: column;
}

/* Form Group Styling */
.form-group {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    font-size: 18px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group input[type="date"],
.form-group input[type="password"] {
    padding: 12px;
    border: 2px solid rgba(164, 179, 255, 0.3);
    border-radius: 8px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: rgba(164, 179, 255, 0.8);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 10px rgba(164, 179, 255, 0.3);
}

/* Gender Buttons */
.gender-buttons {
    display: flex;
    gap: 15px;
    margin-top: 8px;
}

.gender-btn {
    flex: 1;
    padding: 15px;
    border: 2px solid rgba(164, 179, 255, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.gender-btn:hover {
    border-color: rgba(164, 179, 255, 0.6);
    background: rgba(255, 255, 255, 0.9);
}

.gender-btn.selected {
    /* Gold highlight to indicate selected state */
    border-color: #FFD700 !important;
    background: rgba(255, 215, 0, 0.25) !important;
    color: #000 !important;
}

.gender-icon {
    font-size: 24px;
    font-weight: bold;
}

/* Language Buttons */
.language-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 8px;
}

.lang-btn {
    padding: 12px;
    border: 2px solid rgba(164, 179, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.lang-btn:hover {
    border-color: rgba(164, 179, 255, 0.6);
    background: rgba(255, 255, 255, 0.9);
}

.lang-btn.selected {
    /* Gold highlight to indicate selected state */
    border-color: #FFD700 !important;
    background: rgba(255, 215, 0, 0.25) !important;
    color: #000 !important;
}

/* Mobile responsive for language buttons */
@media (max-width: 768px) {
    .language-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Username Validation */
.username-hint {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.username-validation {
    margin-top: 8px !important;
    font-size: 14px !important;
    padding: 8px 12px 8px 45px !important;
    border-radius: 6px !important;
    display: block !important;
    transition: all 0.3s ease !important;
    min-height: 40px !important;
    position: relative !important;
    line-height: 24px !important;
    z-index: 10 !important;
}

.username-validation.checking {
    color: #1565C0 !important;
    background: rgba(33, 150, 243, 0.2) !important;
    border: 2px solid rgba(33, 150, 243, 0.5) !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2) !important;
}

.username-validation.checking::before {
    content: "👤⏳" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 11 !important;
    display: block !important;
}

.username-validation.error {
    color: #FF9800 !important;
    background: rgba(255, 152, 0, 0.2) !important;
    border: 2px solid rgba(255, 152, 0, 0.5) !important;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2) !important;
}

.username-validation.error::before {
    content: "👤⚠️" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 11 !important;
    display: block !important;
}

.username-validation.valid {
    color: #2E7D32 !important;
    background: rgba(76, 175, 80, 0.2) !important;
    border: 2px solid rgba(76, 175, 80, 0.5) !important;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2) !important;
}

.username-validation.valid::before {
    content: "👤✅" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 11 !important;
    display: block !important;
}

.username-validation.invalid {
    color: #C62828 !important;
    background: rgba(244, 67, 54, 0.2) !important;
    border: 2px solid rgba(244, 67, 54, 0.5) !important;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2) !important;
}

.username-validation.invalid::before {
    content: "👤❌" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 11 !important;
    display: block !important;
}

/* Age Validation */
.age-validation {
    margin-top: 8px !important;
    font-size: 14px !important;
    padding: 8px 12px 8px 45px !important;
    border-radius: 6px !important;
    display: block !important;
    transition: all 0.3s ease !important;
    min-height: 40px !important;
    position: relative !important;
    line-height: 24px !important;
}

.age-validation.valid {
    color: #2E7D32 !important;
    background: rgba(76, 175, 80, 0.2) !important;
    border: 2px solid rgba(76, 175, 80, 0.5) !important;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2) !important;
}

.age-validation.valid::before {
    content: "🎂✅" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

.age-validation.invalid {
    color: #C62828 !important;
    background: rgba(244, 67, 54, 0.2) !important;
    border: 2px solid rgba(244, 67, 54, 0.5) !important;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2) !important;
}

.age-validation.invalid::before {
    content: "🎂❌" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

/* Email Validation */
.email-validation {
    margin-top: 8px !important;
    font-size: 14px !important;
    padding: 8px 12px 8px 45px !important;
    border-radius: 6px !important;
    display: block !important;
    transition: all 0.3s ease !important;
    min-height: 40px !important;
    position: relative !important;
    line-height: 24px !important;
}

.email-validation.valid {
    color: #2E7D32 !important;
    background: rgba(76, 175, 80, 0.2) !important;
    border: 2px solid rgba(76, 175, 80, 0.5) !important;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2) !important;
}

.email-validation.valid::before {
    content: "✉️✅" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

.email-validation.invalid {
    color: #C62828 !important;
    background: rgba(244, 67, 54, 0.2) !important;
    border: 2px solid rgba(244, 67, 54, 0.5) !important;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2) !important;
}

.email-validation.invalid::before {
    content: "✉️❌" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

/* Phone Input Group */
.phone-input-group {
    display: flex;
    gap: 10px;
}

.phone-input-group input {
    flex: 1;
}

.sms-btn, .verify-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: rgba(164, 179, 255, 0.8);
    color: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.sms-btn:hover, .verify-btn:hover {
    background: rgba(164, 179, 255, 1);
}

.sms-btn:disabled, .verify-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* SMS Verification */
.sms-verification {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.sms-verification input {
    flex: 1;
    text-align: center;
    font-size: 18px;
    letter-spacing: 2px;
}

.sms-status {
    margin-top: 8px !important;
    font-size: 14px !important;
    padding: 8px 12px 8px 45px !important;
    border-radius: 6px !important;
    display: block !important;
    transition: all 0.3s ease !important;
    min-height: 40px !important;
    position: relative !important;
    line-height: 24px !important;
}

.sms-status.success {
    color: #2E7D32 !important;
    background: rgba(76, 175, 80, 0.2) !important;
    border: 2px solid rgba(76, 175, 80, 0.5) !important;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2) !important;
}

.sms-status.success::before {
    content: "📱✅" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

.sms-status.error {
    color: #C62828 !important;
    background: rgba(244, 67, 54, 0.2) !important;
    border: 2px solid rgba(244, 67, 54, 0.5) !important;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2) !important;
}

.sms-status.error::before {
    content: "📱❌" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

.sms-status.info {
    color: #1565C0 !important;
    background: rgba(33, 150, 243, 0.2) !important;
    border: 2px solid rgba(33, 150, 243, 0.5) !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2) !important;
}

.sms-status.info::before {
    content: "📱📤" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

/* Password Requirements */
.password-requirements {
    margin-top: 10px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    border: 1px solid rgba(164, 179, 255, 0.3);
}

.requirement-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
}

.requirement {
    font-size: 14px !important;
    margin: 4px 0 !important;
    color: #666 !important;
    transition: all 0.3s ease !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    border: 1px solid transparent !important;
}

.requirement.valid {
    color: #2E7D32 !important;
    background: rgba(76, 175, 80, 0.2) !important;
    border: 1px solid rgba(76, 175, 80, 0.4) !important;
    box-shadow: 0 1px 4px rgba(76, 175, 80, 0.2) !important;
}

.requirement.valid::before {
    content: "🔐✅" !important;
    font-size: 16px !important;
    margin-right: 4px !important;
}

.requirement.invalid {
    color: #C62828 !important;
    background: rgba(244, 67, 54, 0.2) !important;
    border: 1px solid rgba(244, 67, 54, 0.4) !important;
    box-shadow: 0 1px 4px rgba(244, 67, 54, 0.2) !important;
}

.requirement.invalid::before {
    content: "🔐❌" !important;
    font-size: 16px !important;
    margin-right: 4px !important;
}

/* Password Match */
.password-match {
    margin-top: 8px !important;
    font-size: 14px !important;
    padding: 8px 12px 8px 45px !important;
    border-radius: 6px !important;
    display: block !important;
    transition: all 0.3s ease !important;
    min-height: 40px !important;
    position: relative !important;
    line-height: 24px !important;
}

.password-match.valid {
    color: #2E7D32 !important;
    background: rgba(76, 175, 80, 0.2) !important;
    border: 2px solid rgba(76, 175, 80, 0.5) !important;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2) !important;
}

.password-match.valid::before {
    content: "🔒✅" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

.password-match.invalid {
    color: #C62828 !important;
    background: rgba(244, 67, 54, 0.2) !important;
    border: 2px solid rgba(244, 67, 54, 0.5) !important;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2) !important;
}

.password-match.invalid::before {
    content: "🔒❌" !important;
    font-size: 18px !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

/* Register Submit Button */
.register-submit-btn {
    width: 100%;
    padding: 15px;
    margin-top: 20px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(164, 179, 255, 0.8) 0%, rgba(255, 171, 188, 0.8) 100%);
    color: white;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.register-submit-btn:hover {
    background: linear-gradient(135deg, rgba(164, 179, 255, 1) 0%, rgba(255, 171, 188, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(164, 179, 255, 0.4);
}

.register-submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

h1, h2 {
    color: #333;
}

a {
    color: #333;
}

.alert {
    padding: 15px;
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
    border-radius: 4px;
    margin-top: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

table, th, td {
    border: 1px solid #ddd;
}

th, td {
    padding: 10px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}
/* Password strength progress bar */
.password-progress-wrapper {
    width: 100%;
    height: 8px;
    background: rgba(164, 179, 255, 0.3);
    border-radius: 4px;
    margin-top: 12px;
    overflow: hidden;
}

.password-progress {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #FF6B6B 0%, #FFD93D 50%, #4CAF50 100%);
    transition: width 0.3s ease;
}