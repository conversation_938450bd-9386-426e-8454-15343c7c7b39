<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Test</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="register-modal" style="display: block;">
        <div class="register-modal-content">
            <h2>Create Account - Test Page</h2>
            
            <div class="form-group">
                <label for="regUsername">Username :</label>
                <input type="text" id="regUsername" placeholder="Enter your username" required>
                <div class="username-hint">Use English letters, numbers, and periods. 6-20 characters.</div>
                <div class="username-validation valid" id="usernameValidation">Username available</div>
            </div>

            <div class="form-group">
                <label>Gender :</label>
                <div class="gender-buttons">
                    <button type="button" class="gender-btn selected" data-gender="male">
                        <span class="gender-icon">♂</span>
                        <span>Male</span>
                    </button>
                    <button type="button" class="gender-btn" data-gender="female">
                        <span class="gender-icon">♀</span>
                        <span>Female</span>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="regBirthday">Birthday :</label>
                <input type="date" id="regBirthday" required>
                <div class="age-validation valid" id="ageValidation">Age verified</div>
            </div>

            <div class="form-group">
                <label>Language :</label>
                <div class="language-buttons">
                    <button type="button" class="lang-btn selected" data-lang="en">English</button>
                    <button type="button" class="lang-btn" data-lang="zh">中文</button>
                    <button type="button" class="lang-btn" data-lang="es">Español</button>
                    <button type="button" class="lang-btn" data-lang="ja">日本語</button>
                    <button type="button" class="lang-btn" data-lang="ko">한국어</button>
                    <button type="button" class="lang-btn" data-lang="other">Other</button>
                </div>
            </div>

            <div class="form-group">
                <label for="regEmail">Email :</label>
                <input type="email" id="regEmail" placeholder="Enter your email" required>
                <div class="email-validation valid" id="emailValidation">Valid email format</div>
            </div>

            <div class="form-group">
                <label for="regPhone">Phone :</label>
                <div class="phone-input-group">
                    <input type="tel" id="regPhone" placeholder="Enter phone number" pattern="[0-9]*" inputmode="numeric" maxlength="15" required>
                    <button type="button" class="sms-btn">Send SMS</button>
                </div>
                <div class="sms-status success" id="smsStatus">SMS sent successfully</div>
            </div>

            <div class="form-group">
                <label for="regPassword">Password :</label>
                <input type="password" id="regPassword" placeholder="Enter your password" required>
                <div class="password-requirements">
                    <div class="requirement-title">Password Requirements:</div>
                    <div class="requirement valid">At least 8 characters</div>
                    <div class="requirement valid">Contains uppercase letter</div>
                    <div class="requirement invalid">Contains lowercase letter</div>
                    <div class="requirement valid">Contains number</div>
                </div>
            </div>

            <div class="form-group">
                <label for="regConfirmPassword">Confirm Password :</label>
                <input type="password" id="regConfirmPassword" placeholder="Confirm your password" required>
                <div class="password-match valid" id="passwordMatch">Passwords match</div>
            </div>

            <button type="submit" class="register-submit-btn">Create Account</button>
        </div>
    </div>

    <script>
        // Test button interactions
        document.querySelectorAll('.gender-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.gender-btn').forEach(b => b.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.lang-btn').forEach(b => b.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // Test validation state changes
        document.getElementById('regUsername').addEventListener('input', function() {
            const validation = document.getElementById('usernameValidation');
            if (this.value.length < 6) {
                validation.className = 'username-validation invalid';
                validation.textContent = 'Username too short';
            } else if (this.value.length > 20) {
                validation.className = 'username-validation invalid';
                validation.textContent = 'Username too long';
            } else {
                validation.className = 'username-validation valid';
                validation.textContent = 'Username available';
            }
        });

        // Test phone numeric input
        document.getElementById('regPhone').addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    </script>
</body>
</html>
