<?php
session_start();
header('Content-Type: application/json');

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $verification_code = trim($_POST['verification_code']);
    
    if (empty($verification_code)) {
        echo json_encode([
            'success' => false,
            'message' => 'Verification code is required.'
        ]);
        exit;
    }
    
    // Check if verification code exists in session
    if (!isset($_SESSION['sms_verification_code']) || !isset($_SESSION['sms_code_time'])) {
        echo json_encode([
            'success' => false,
            'message' => 'No verification code found. Please request a new code.'
        ]);
        exit;
    }
    
    // Check if code has expired (5 minutes)
    if (time() - $_SESSION['sms_code_time'] > 300) {
        unset($_SESSION['sms_verification_code']);
        unset($_SESSION['sms_phone_number']);
        unset($_SESSION['sms_code_time']);
        
        echo json_encode([
            'success' => false,
            'message' => 'Verification code has expired. Please request a new code.'
        ]);
        exit;
    }
    
    // Verify the code
    if ($verification_code === $_SESSION['sms_verification_code']) {
        // Mark as verified
        $_SESSION['sms_verified'] = true;
        
        echo json_encode([
            'success' => true,
            'message' => 'Phone number verified successfully.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid verification code.'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
